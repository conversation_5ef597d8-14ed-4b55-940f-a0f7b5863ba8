import { Request, Response, NextFunction } from "express";
import { FileValidationService } from "../services/FileValidationService";
import { QuotaManagementService } from "../services/QuotaManagementService";
import logger from "../utils/logger";
import rateLimit from "express-rate-limit";

/**
 * 扩展Request接口以包含用户信息
 */
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        type: string;
        permissions?: string[];
      };
    }
  }
}

/**
 * 文件上传安全中间件
 */
export class SecurityMiddleware {
  private fileValidationService: FileValidationService;
  private quotaManagementService: QuotaManagementService;

  constructor() {
    this.fileValidationService = new FileValidationService();
    this.quotaManagementService = new QuotaManagementService();
  }

  /**
   * 用户认证中间件（简化版本）
   */
  authenticate = (req: Request, res: Response, next: NextFunction): void => {
    try {
      // 从请求头获取用户ID（实际应用中应该验证JWT token等）
      const userId = req.headers["x-user-id"] as string;
      const userType = req.headers["x-user-type"] as string || "free";

      if (!userId) {
        res.status(401).json({ error: "Authentication required" });
        return;
      }

      // 设置用户信息到请求对象
      req.user = {
        id: userId,
        type: userType,
      };

      next();
    } catch (error) {
      logger.error("Authentication error:", error);
      res.status(401).json({ error: "Invalid authentication" });
    }
  };

  /**
   * 文件上传验证中间件
   */
  validateFileUpload = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { fileName, fileType, fileSize } = req.body;
      const userId = req.user?.id;
      const userType = req.user?.type || "free";

      if (!userId) {
        res.status(401).json({ error: "User authentication required" });
        return;
      }

      if (!fileName || !fileType || !fileSize) {
        res.status(400).json({
          error: "Missing required fields: fileName, fileType, fileSize",
        });
        return;
      }

      // 1. 验证文件
      const validationResult = await this.fileValidationService.validateFile(
        fileName,
        fileSize,
        fileType
      );

      if (!validationResult.isValid) {
        res.status(400).json({
          error: "File validation failed",
          details: validationResult.errors,
        });
        return;
      }

      // 2. 检查配额
      const quotaCheck = await this.quotaManagementService.checkUploadQuota(
        userId,
        fileSize,
        userType
      );

      if (!quotaCheck.allowed) {
        res.status(413).json({
          error: "Quota exceeded",
          reason: quotaCheck.reason,
          remainingStorage: quotaCheck.remainingStorage,
          remainingFileCount: quotaCheck.remainingFileCount,
        });
        return;
      }

      // 3. 添加警告到响应头
      if (quotaCheck.warnings && quotaCheck.warnings.length > 0) {
        res.setHeader("X-Quota-Warnings", quotaCheck.warnings.join("; "));
      }

      if (validationResult.warnings && validationResult.warnings.length > 0) {
        res.setHeader("X-Validation-Warnings", validationResult.warnings.join("; "));
      }

      next();
    } catch (error) {
      logger.error("File upload validation error:", error);
      res.status(500).json({
        error: "Validation failed",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  };

  /**
   * 权限检查中间件
   */
  checkPermission = (requiredPermission: string) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      const user = req.user;

      if (!user) {
        res.status(401).json({ error: "Authentication required" });
        return;
      }

      // 管理员拥有所有权限
      if (user.type === "admin") {
        next();
        return;
      }

      // 检查用户权限
      if (user.permissions && user.permissions.includes(requiredPermission)) {
        next();
        return;
      }

      // 检查基于用户类型的默认权限
      const defaultPermissions = this.getDefaultPermissions(user.type);
      if (defaultPermissions.includes(requiredPermission)) {
        next();
        return;
      }

      res.status(403).json({
        error: "Insufficient permissions",
        required: requiredPermission,
      });
    };
  };

  /**
   * 获取用户类型的默认权限
   */
  private getDefaultPermissions(userType: string): string[] {
    const permissionMap: { [key: string]: string[] } = {
      free: ["upload:basic", "download:own", "delete:own"],
      premium: ["upload:advanced", "download:own", "delete:own", "share:basic"],
      enterprise: [
        "upload:advanced",
        "download:own",
        "delete:own",
        "share:advanced",
        "manage:team",
      ],
      admin: ["*"], // 所有权限
    };

    return permissionMap[userType] || [];
  }

  /**
   * 资源所有权验证中间件
   */
  validateResourceOwnership = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const resourceId = req.params.id;

      if (!userId) {
        res.status(401).json({ error: "Authentication required" });
        return;
      }

      if (!resourceId) {
        res.status(400).json({ error: "Resource ID required" });
        return;
      }

      // 这里应该检查资源是否属于当前用户
      // 暂时跳过实际的所有权检查
      // 在实际应用中，应该查询数据库验证资源所有权

      next();
    } catch (error) {
      logger.error("Resource ownership validation error:", error);
      res.status(500).json({
        error: "Ownership validation failed",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  };

  /**
   * 安全头中间件
   */
  securityHeaders = (req: Request, res: Response, next: NextFunction): void => {
    // 设置安全相关的响应头
    res.setHeader("X-Content-Type-Options", "nosniff");
    res.setHeader("X-Frame-Options", "DENY");
    res.setHeader("X-XSS-Protection", "1; mode=block");
    res.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");
    
    // 移除可能泄露服务器信息的头
    res.removeHeader("X-Powered-By");
    
    next();
  };

  /**
   * 请求大小限制中间件
   */
  requestSizeLimit = (maxSize: number = 50 * 1024 * 1024) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      const contentLength = parseInt(req.headers["content-length"] || "0");
      
      if (contentLength > maxSize) {
        res.status(413).json({
          error: "Request entity too large",
          maxSize,
          received: contentLength,
        });
        return;
      }
      
      next();
    };
  };

  /**
   * IP白名单中间件
   */
  ipWhitelist = (allowedIPs: string[]) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      const clientIP = req.ip || req.connection.remoteAddress || "";
      
      if (allowedIPs.length > 0 && !allowedIPs.includes(clientIP)) {
        logger.warn(`Access denied for IP: ${clientIP}`);
        res.status(403).json({ error: "Access denied" });
        return;
      }
      
      next();
    };
  };
}

/**
 * 创建上传专用的速率限制器
 */
export const createUploadRateLimit = (windowMs: number = 15 * 60 * 1000, max: number = 10) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      error: "Too many upload requests",
      retryAfter: Math.ceil(windowMs / 1000),
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
      // 基于用户ID而不是IP进行限制
      return req.user?.id || req.ip || "anonymous";
    },
    skip: (req) => {
      // 管理员和企业用户跳过限制
      return req.user?.type === "admin" || req.user?.type === "enterprise";
    },
  });
};

/**
 * 创建API访问速率限制器
 */
export const createAPIRateLimit = (windowMs: number = 15 * 60 * 1000, max: number = 100) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      error: "Too many API requests",
      retryAfter: Math.ceil(windowMs / 1000),
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
      return req.user?.id || req.ip || "anonymous";
    },
    skip: (req) => {
      return req.user?.type === "admin";
    },
  });
};

// 导出单例实例
export const securityMiddleware = new SecurityMiddleware();

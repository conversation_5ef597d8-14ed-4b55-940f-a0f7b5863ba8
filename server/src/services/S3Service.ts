import {
  S3<PERSON>lient,
  PutObjectCommand,
  GetO<PERSON>Command,
  DeleteObjectCommand,
  ListObjectsV2Command,
  CreateMultipartUploadCommand,
  UploadPartCommand,
  CompleteMultipartUploadCommand,
  AbortMultipartUploadCommand,
  HeadObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import {
  createS3Client,
  getS3Config,
  generateFileKey,
  isFileTypeAllowed,
  isFileSizeAllowed,
} from "../config/s3Config";
import { MediaRepository } from "../repositories/MediaRepository";
import { ThumbnailService } from "./ThumbnailService";
import logger from "../utils/logger";
import {
  SignedUrlRequest,
  SignedUrlResponse,
  MultipartUploadRequest,
  MultipartUploadResponse,
  PartUploadUrlRequest,
  PartUploadUrlResponse,
  CompleteMultipartUploadRequest,
  UploadCompleteRequest,
  MediaMetadata,
  FileValidationError,
} from "../types/media";

/**
 * S3服务类
 * 处理所有S3相关操作，包括签名URL生成、文件上传、删除等
 */
export class S3Service {
  private s3Client: S3Client;
  private config: ReturnType<typeof getS3Config>;
  private mediaRepository: MediaRepository;
  private thumbnailService: ThumbnailService;

  constructor() {
    this.s3Client = createS3Client();
    this.config = getS3Config();
    this.mediaRepository = new MediaRepository();
    this.thumbnailService = new ThumbnailService();
  }

  /**
   * 验证文件
   */
  private validateFile(
    fileName: string,
    fileType: string,
    fileSize: number
  ): FileValidationError | null {
    // 验证文件类型
    if (!isFileTypeAllowed(fileType)) {
      return {
        code: "INVALID_FILE_TYPE",
        message: `File type ${fileType} is not allowed`,
        value: fileType,
        limit: this.config.allowedFileTypes,
      };
    }

    // 验证文件大小
    if (!isFileSizeAllowed(fileSize)) {
      return {
        code: "FILE_TOO_LARGE",
        message: `File size ${fileSize} exceeds maximum allowed size`,
        value: fileSize,
        limit: this.config.maxFileSize,
      };
    }

    // 验证文件名
    if (!fileName || fileName.trim().length === 0) {
      return {
        code: "INVALID_FILE_NAME",
        message: "File name cannot be empty",
        value: fileName,
      };
    }

    return null;
  }

  /**
   * 检查用户存储配额
   */
  private async checkUserQuota(
    userId: string,
    fileSize: number
  ): Promise<FileValidationError | null> {
    const quota = await this.mediaRepository.getUserStorageQuota(userId);
    const currentUsage = await this.mediaRepository.calculateUserStorage(
      userId
    );

    // 检查存储空间配额
    if (currentUsage.usedStorage + fileSize > quota.storageLimit) {
      return {
        code: "QUOTA_EXCEEDED",
        message: "Storage quota exceeded",
        value: currentUsage.usedStorage + fileSize,
        limit: quota.storageLimit,
      };
    }

    // 检查文件数量配额
    if (currentUsage.fileCount >= quota.fileCountLimit) {
      return {
        code: "QUOTA_EXCEEDED",
        message: "File count quota exceeded",
        value: currentUsage.fileCount,
        limit: quota.fileCountLimit,
      };
    }

    return null;
  }

  /**
   * 生成单文件上传的签名URL
   */
  async generateSignedUrl(
    request: SignedUrlRequest,
    userId: string
  ): Promise<SignedUrlResponse> {
    const { fileName, fileType, fileSize } = request;

    // 验证文件
    const fileValidation = this.validateFile(fileName, fileType, fileSize);
    if (fileValidation) {
      throw new Error(fileValidation.message);
    }

    // 检查用户配额
    const quotaValidation = await this.checkUserQuota(userId, fileSize);
    if (quotaValidation) {
      throw new Error(quotaValidation.message);
    }

    // 生成文件键
    const fileKey = generateFileKey(userId, fileName, fileType);

    // 创建PUT命令
    const putCommand = new PutObjectCommand({
      Bucket: this.config.bucketName,
      Key: fileKey,
      ContentType: fileType,
      ContentLength: fileSize,
      Metadata: {
        originalName: fileName,
        userId: userId,
        uploadDate: new Date().toISOString(),
      },
    });

    // 生成签名URL
    const signedUrl = await getSignedUrl(this.s3Client, putCommand, {
      expiresIn: this.config.signedUrlExpires,
    });

    const expiresAt = Date.now() + this.config.signedUrlExpires * 1000;

    logger.info(
      `Generated signed URL for user ${userId}, file: ${fileName}, key: ${fileKey}`
    );

    return {
      url: signedUrl,
      fileKey,
      method: "PUT",
      expiresAt,
    };
  }

  /**
   * 初始化多部分上传
   */
  async initiateMultipartUpload(
    request: MultipartUploadRequest,
    userId: string
  ): Promise<MultipartUploadResponse> {
    const {
      fileName,
      fileType,
      fileSize,
      partSize = 5 * 1024 * 1024,
    } = request; // 默认5MB分片

    // 验证文件
    const fileValidation = this.validateFile(fileName, fileType, fileSize);
    if (fileValidation) {
      throw new Error(fileValidation.message);
    }

    // 检查用户配额
    const quotaValidation = await this.checkUserQuota(userId, fileSize);
    if (quotaValidation) {
      throw new Error(quotaValidation.message);
    }

    // 生成文件键
    const fileKey = generateFileKey(userId, fileName, fileType);

    // 创建多部分上传
    const createCommand = new CreateMultipartUploadCommand({
      Bucket: this.config.bucketName,
      Key: fileKey,
      ContentType: fileType,
      Metadata: {
        originalName: fileName,
        userId: userId,
        uploadDate: new Date().toISOString(),
        fileSize: fileSize.toString(),
      },
    });

    const response = await this.s3Client.send(createCommand);
    const uploadId = response.UploadId!;
    const totalParts = Math.ceil(fileSize / partSize);

    logger.info(
      `Initiated multipart upload for user ${userId}, file: ${fileName}, uploadId: ${uploadId}`
    );

    return {
      uploadId,
      fileKey,
      partSize,
      totalParts,
    };
  }

  /**
   * 生成分片上传的签名URL
   */
  async generatePartUploadUrl(
    request: PartUploadUrlRequest
  ): Promise<PartUploadUrlResponse> {
    const { uploadId, fileKey, partNumber } = request;

    const uploadCommand = new UploadPartCommand({
      Bucket: this.config.bucketName,
      Key: fileKey,
      UploadId: uploadId,
      PartNumber: partNumber,
    });

    const signedUrl = await getSignedUrl(this.s3Client, uploadCommand, {
      expiresIn: this.config.signedUrlExpires,
    });

    const expiresAt = Date.now() + this.config.signedUrlExpires * 1000;

    return {
      url: signedUrl,
      partNumber,
      expiresAt,
    };
  }

  /**
   * 完成多部分上传
   */
  async completeMultipartUpload(
    request: CompleteMultipartUploadRequest,
    userId: string
  ): Promise<MediaMetadata> {
    const { uploadId, fileKey, parts, metadata } = request;

    // 完成多部分上传
    const completeCommand = new CompleteMultipartUploadCommand({
      Bucket: this.config.bucketName,
      Key: fileKey,
      UploadId: uploadId,
      MultipartUpload: {
        Parts: parts.map((part) => ({
          ETag: part.etag,
          PartNumber: part.partNumber,
        })),
      },
    });

    await this.s3Client.send(completeCommand);

    // 保存媒体元数据
    if (metadata) {
      const mediaMetadata = await this.saveMediaMetadata({
        userId,
        fileName: metadata.fileName,
        fileKey,
        fileType: metadata.fileType,
        fileSize: metadata.fileSize,
        width: metadata.width,
        height: metadata.height,
        duration: metadata.duration,
      });

      logger.info(
        `Completed multipart upload for user ${userId}, file: ${metadata.fileName}, key: ${fileKey}`
      );
      return mediaMetadata;
    }

    throw new Error("Metadata is required for completing multipart upload");
  }

  /**
   * 取消多部分上传
   */
  async abortMultipartUpload(uploadId: string, fileKey: string): Promise<void> {
    const abortCommand = new AbortMultipartUploadCommand({
      Bucket: this.config.bucketName,
      Key: fileKey,
      UploadId: uploadId,
    });

    await this.s3Client.send(abortCommand);
    logger.info(`Aborted multipart upload: ${uploadId}, key: ${fileKey}`);
  }

  /**
   * 处理上传完成
   */
  async handleUploadComplete(
    request: UploadCompleteRequest,
    userId: string
  ): Promise<MediaMetadata> {
    const { fileKey, fileName, fileType, fileSize, metadata } = request;

    // 验证文件是否存在于S3
    try {
      const headCommand = new HeadObjectCommand({
        Bucket: this.config.bucketName,
        Key: fileKey,
      });
      await this.s3Client.send(headCommand);
    } catch (error) {
      throw new Error(`File not found in S3: ${fileKey}`);
    }

    // 保存媒体元数据
    const mediaMetadata = await this.saveMediaMetadata({
      userId,
      fileName,
      fileKey,
      fileType,
      fileSize,
      width: metadata?.width,
      height: metadata?.height,
      duration: metadata?.duration,
    });

    logger.info(
      `Upload completed for user ${userId}, file: ${fileName}, key: ${fileKey}`
    );
    return mediaMetadata;
  }

  /**
   * 保存媒体元数据
   */
  private async saveMediaMetadata(data: {
    userId: string;
    fileName: string;
    fileKey: string;
    fileType: string;
    fileSize: number;
    width?: number;
    height?: number;
    duration?: number;
  }): Promise<MediaMetadata> {
    const url = `https://${this.config.bucketName}.s3.${this.config.region}.amazonaws.com/${data.fileKey}`;

    const metadata = await this.mediaRepository.saveMediaMetadata({
      userId: data.userId,
      fileName: data.fileName,
      fileKey: data.fileKey,
      fileType: data.fileType,
      fileSize: data.fileSize,
      uploadDate: new Date(),
      url,
      width: data.width,
      height: data.height,
      duration: data.duration,
      status: "ready",
    });

    // 更新用户存储配额
    const currentUsage = await this.mediaRepository.calculateUserStorage(
      data.userId
    );
    await this.mediaRepository.updateUserStorageQuota(data.userId, {
      usedStorage: currentUsage.usedStorage,
      currentFileCount: currentUsage.fileCount,
    });

    // 异步生成缩略图（对于图片和视频）
    if (
      data.fileType.startsWith("image/") ||
      data.fileType.startsWith("video/")
    ) {
      this.thumbnailService
        .createThumbnailTask(metadata.id, data.fileKey, data.fileType, [
          "small",
          "medium",
          "large",
        ])
        .catch((error) => {
          logger.error(
            `Failed to create thumbnail task for ${metadata.id}:`,
            error
          );
        });
    }

    return metadata;
  }

  /**
   * 删除文件
   */
  async deleteFile(fileKey: string, userId: string): Promise<boolean> {
    try {
      // 从S3删除文件
      const deleteCommand = new DeleteObjectCommand({
        Bucket: this.config.bucketName,
        Key: fileKey,
      });
      await this.s3Client.send(deleteCommand);

      // 从数据库删除元数据
      const media = await this.mediaRepository.getMediaByFileKey(fileKey);
      if (media && media.userId === userId) {
        await this.mediaRepository.deleteMedia(media.id);

        // 更新用户存储配额
        const currentUsage = await this.mediaRepository.calculateUserStorage(
          userId
        );
        await this.mediaRepository.updateUserStorageQuota(userId, {
          usedStorage: currentUsage.usedStorage,
          currentFileCount: currentUsage.fileCount,
        });
      }

      logger.info(`File deleted: ${fileKey} by user ${userId}`);
      return true;
    } catch (error) {
      logger.error(`Failed to delete file ${fileKey}:`, error);
      return false;
    }
  }

  /**
   * 列出用户的文件
   */
  async listUserFiles(userId: string, prefix?: string): Promise<string[]> {
    const listCommand = new ListObjectsV2Command({
      Bucket: this.config.bucketName,
      Prefix: prefix || `uploads/user-${userId}/`,
    });

    const response = await this.s3Client.send(listCommand);
    return response.Contents?.map((obj) => obj.Key!) || [];
  }

  /**
   * 获取文件的公共URL
   */
  getPublicUrl(fileKey: string): string {
    return `https://${this.config.bucketName}.s3.${this.config.region}.amazonaws.com/${fileKey}`;
  }

  /**
   * 生成临时访问URL
   */
  async generateTemporaryUrl(
    fileKey: string,
    expiresIn: number = 3600
  ): Promise<string> {
    const getCommand = new GetObjectCommand({
      Bucket: this.config.bucketName,
      Key: fileKey,
    });

    return await getSignedUrl(this.s3Client, getCommand, { expiresIn });
  }
}

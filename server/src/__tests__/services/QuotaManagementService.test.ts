import { QuotaManagementService } from "../../services/QuotaManagementService";
import { MediaRepository } from "../../repositories/MediaRepository";

// Mock MediaRepository
jest.mock("../../repositories/MediaRepository");

describe("QuotaManagementService", () => {
  let quotaService: QuotaManagementService;
  let mockMediaRepository: jest.Mocked<MediaRepository>;

  beforeEach(() => {
    jest.clearAllMocks();

    mockMediaRepository = {
      getUserStorageQuota: jest.fn(),
      calculateUserStorage: jest.fn(),
      updateUserStorageQuota: jest.fn(),
      getMediaByUser: jest.fn(),
    } as any;

    (MediaRepository as jest.Mock).mockImplementation(
      () => mockMediaRepository
    );

    quotaService = new QuotaManagementService();
  });

  describe("checkUploadQuota", () => {
    it("should allow upload for free user within limits", async () => {
      const userId = "test-user";
      const fileSize = 10 * 1024 * 1024; // 10MB
      const userType = "free";

      mockMediaRepository.getUserStorageQuota.mockResolvedValue({
        userId,
        usedStorage: 100 * 1024 * 1024, // 100MB used
        storageLimit: 1024 * 1024 * 1024, // 1GB limit
        fileCountLimit: 100,
        currentFileCount: 50,
        lastUpdated: new Date(),
      });

      mockMediaRepository.calculateUserStorage.mockResolvedValue({
        usedStorage: 100 * 1024 * 1024,
        fileCount: 50,
      });

      const result = await quotaService.checkUploadQuota(
        userId,
        fileSize,
        userType
      );

      expect(result.allowed).toBe(true);
      expect(result.remainingStorage).toBe(
        1024 * 1024 * 1024 - 100 * 1024 * 1024 - fileSize
      );
      expect(result.remainingFileCount).toBe(100 - 50 - 1);
    });

    it("should reject upload when file size exceeds maximum", async () => {
      const userId = "test-user";
      const fileSize = 100 * 1024 * 1024; // 100MB (exceeds 50MB limit for free users)
      const userType = "free";

      const result = await quotaService.checkUploadQuota(
        userId,
        fileSize,
        userType
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain("exceeds maximum allowed size");
    });

    it("should reject upload when storage quota is exceeded", async () => {
      const userId = "test-user";
      const fileSize = 100 * 1024 * 1024; // 100MB
      const userType = "free";

      mockMediaRepository.getUserStorageQuota.mockResolvedValue({
        userId,
        usedStorage: 950 * 1024 * 1024, // 950MB used
        storageLimit: 1024 * 1024 * 1024, // 1GB limit
        fileCountLimit: 100,
        currentFileCount: 50,
        lastUpdated: new Date(),
      });

      mockMediaRepository.calculateUserStorage.mockResolvedValue({
        usedStorage: 950 * 1024 * 1024,
        fileCount: 50,
      });

      const result = await quotaService.checkUploadQuota(
        userId,
        fileSize,
        userType
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain("Storage quota exceeded");
      expect(result.remainingStorage).toBe(
        1024 * 1024 * 1024 - 950 * 1024 * 1024
      );
    });

    it("should reject upload when file count limit is reached", async () => {
      const userId = "test-user";
      const fileSize = 10 * 1024 * 1024; // 10MB
      const userType = "free";

      mockMediaRepository.getUserStorageQuota.mockResolvedValue({
        userId,
        usedStorage: 100 * 1024 * 1024,
        storageLimit: 1024 * 1024 * 1024,
        fileCountLimit: 100,
        currentFileCount: 100, // At limit
        lastUpdated: new Date(),
      });

      mockMediaRepository.calculateUserStorage.mockResolvedValue({
        usedStorage: 100 * 1024 * 1024,
        fileCount: 100,
      });

      const result = await quotaService.checkUploadQuota(
        userId,
        fileSize,
        userType
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain("File count limit exceeded");
      expect(result.remainingFileCount).toBe(0);
    });

    it("should provide warnings when approaching limits", async () => {
      const userId = "test-user";
      const fileSize = 100 * 1024 * 1024; // 100MB
      const userType = "free";

      mockMediaRepository.getUserStorageQuota.mockResolvedValue({
        userId,
        usedStorage: 800 * 1024 * 1024, // 800MB used (will be 900MB after upload = 87.9%)
        storageLimit: 1024 * 1024 * 1024, // 1GB limit
        fileCountLimit: 100,
        currentFileCount: 85, // Will be 86 after upload = 86%
        lastUpdated: new Date(),
      });

      mockMediaRepository.calculateUserStorage.mockResolvedValue({
        usedStorage: 800 * 1024 * 1024,
        fileCount: 85,
      });

      const result = await quotaService.checkUploadQuota(
        userId,
        fileSize,
        userType
      );

      expect(result.allowed).toBe(true);
      expect(result.warnings).toBeDefined();
      expect(result.warnings!.length).toBeGreaterThan(0);
      expect(result.warnings!.some((w) => w.includes("Storage usage"))).toBe(
        true
      );
      expect(result.warnings!.some((w) => w.includes("File count"))).toBe(true);
    });

    it("should allow unlimited uploads for admin users", async () => {
      const userId = "admin-user";
      const fileSize = 10 * 1024 * 1024 * 1024; // 10GB
      const userType = "admin";

      mockMediaRepository.getUserStorageQuota.mockResolvedValue({
        userId,
        usedStorage: 0,
        storageLimit: Number.MAX_SAFE_INTEGER,
        fileCountLimit: Number.MAX_SAFE_INTEGER,
        currentFileCount: 0,
        lastUpdated: new Date(),
      });

      mockMediaRepository.calculateUserStorage.mockResolvedValue({
        usedStorage: 0,
        fileCount: 0,
      });

      const result = await quotaService.checkUploadQuota(
        userId,
        fileSize,
        userType
      );

      expect(result.allowed).toBe(true);
    });

    it("should handle unknown user type", async () => {
      const userId = "test-user";
      const fileSize = 10 * 1024 * 1024;
      const userType = "unknown";

      const result = await quotaService.checkUploadQuota(
        userId,
        fileSize,
        userType
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain("Unknown user type");
    });
  });

  describe("getQuotaUsageStats", () => {
    it("should return comprehensive usage statistics", async () => {
      const userId = "test-user";
      const userType = "premium";

      mockMediaRepository.calculateUserStorage.mockResolvedValue({
        usedStorage: 2 * 1024 * 1024 * 1024, // 2GB
        fileCount: 500,
      });

      // Mock daily and monthly usage
      mockMediaRepository.getMediaByUser.mockResolvedValue({
        items: [],
        total: 0,
        page: 1,
        limit: 1000,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });

      const stats = await quotaService.getQuotaUsageStats(userId, userType);

      expect(stats.userId).toBe(userId);
      expect(stats.userType).toBe(userType);
      expect(stats.currentUsage.storageUsed).toBe(2 * 1024 * 1024 * 1024);
      expect(stats.currentUsage.fileCount).toBe(500);
      expect(stats.currentUsage.storagePercentage).toBe(20); // 2GB / 10GB = 20%
      expect(stats.currentUsage.fileCountPercentage).toBe(50); // 500 / 1000 = 50%
      expect(stats.limits.storageLimit).toBe(10 * 1024 * 1024 * 1024); // 10GB for premium
      expect(stats.limits.fileCountLimit).toBe(1000);
      expect(stats.dailyUsage).toBeDefined();
      expect(stats.monthlyUsage).toBeDefined();
    });

    it("should include warnings when usage is high", async () => {
      const userId = "test-user";
      const userType = "free";

      mockMediaRepository.calculateUserStorage.mockResolvedValue({
        usedStorage: 950 * 1024 * 1024, // 950MB (95% of 1GB)
        fileCount: 95, // 95% of 100
      });

      mockMediaRepository.getMediaByUser.mockResolvedValue({
        items: [],
        total: 0,
        page: 1,
        limit: 1000,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });

      const stats = await quotaService.getQuotaUsageStats(userId, userType);

      expect(stats.warnings).toBeDefined();
      expect(stats.warnings.length).toBeGreaterThan(0);
      expect(
        stats.warnings.some((w) => w.includes("Storage usage is over 90%"))
      ).toBe(true);
      expect(
        stats.warnings.some((w) => w.includes("File count is over 90%"))
      ).toBe(true);
    });

    it("should throw error for unknown user type", async () => {
      const userId = "test-user";
      const userType = "unknown";

      await expect(
        quotaService.getQuotaUsageStats(userId, userType)
      ).rejects.toThrow("Unknown user type");
    });
  });

  describe("quota policies", () => {
    it("should return correct policy for free users", () => {
      const policy = quotaService.getQuotaPolicy("free");

      expect(policy).toBeDefined();
      expect(policy!.userType).toBe("free");
      expect(policy!.storageLimit).toBe(1024 * 1024 * 1024); // 1GB
      expect(policy!.fileCountLimit).toBe(100);
      expect(policy!.maxFileSize).toBe(50 * 1024 * 1024); // 50MB
    });

    it("should return correct policy for premium users", () => {
      const policy = quotaService.getQuotaPolicy("premium");

      expect(policy).toBeDefined();
      expect(policy!.userType).toBe("premium");
      expect(policy!.storageLimit).toBe(10 * 1024 * 1024 * 1024); // 10GB
      expect(policy!.fileCountLimit).toBe(1000);
      expect(policy!.maxFileSize).toBe(200 * 1024 * 1024); // 200MB
    });

    it("should return correct policy for enterprise users", () => {
      const policy = quotaService.getQuotaPolicy("enterprise");

      expect(policy).toBeDefined();
      expect(policy!.userType).toBe("enterprise");
      expect(policy!.storageLimit).toBe(100 * 1024 * 1024 * 1024); // 100GB
      expect(policy!.fileCountLimit).toBe(10000);
      expect(policy!.maxFileSize).toBe(1024 * 1024 * 1024); // 1GB
    });

    it("should return correct policy for admin users", () => {
      const policy = quotaService.getQuotaPolicy("admin");

      expect(policy).toBeDefined();
      expect(policy!.userType).toBe("admin");
      expect(policy!.storageLimit).toBe(Number.MAX_SAFE_INTEGER);
      expect(policy!.fileCountLimit).toBe(Number.MAX_SAFE_INTEGER);
      expect(policy!.maxFileSize).toBe(Number.MAX_SAFE_INTEGER);
    });

    it("should allow setting custom quota policy", () => {
      const customPolicy = {
        userType: "free" as const, // 使用已有的类型
        storageLimit: 5 * 1024 * 1024 * 1024, // 5GB
        fileCountLimit: 500,
        maxFileSize: 100 * 1024 * 1024, // 100MB
      };

      quotaService.setQuotaPolicy("custom", customPolicy);
      const retrievedPolicy = quotaService.getQuotaPolicy("custom");

      expect(retrievedPolicy).toEqual(customPolicy);
    });

    it("should return all quota policies", () => {
      const allPolicies = quotaService.getAllQuotaPolicies();

      expect(allPolicies).toHaveProperty("free");
      expect(allPolicies).toHaveProperty("premium");
      expect(allPolicies).toHaveProperty("enterprise");
      expect(allPolicies).toHaveProperty("admin");
      expect(Object.keys(allPolicies)).toHaveLength(4);
    });
  });

  describe("updateUserQuota", () => {
    it("should update user quota through media repository", async () => {
      const userId = "test-user";
      const updates = {
        storageLimit: 2 * 1024 * 1024 * 1024, // 2GB
        fileCountLimit: 200,
      };

      const expectedQuota = {
        userId,
        usedStorage: 100 * 1024 * 1024,
        storageLimit: 2 * 1024 * 1024 * 1024,
        fileCountLimit: 200,
        currentFileCount: 50,
        lastUpdated: new Date(),
      };

      mockMediaRepository.updateUserStorageQuota.mockResolvedValue(
        expectedQuota
      );

      const result = await quotaService.updateUserQuota(userId, updates);

      expect(result).toEqual(expectedQuota);
      expect(mockMediaRepository.updateUserStorageQuota).toHaveBeenCalledWith(
        userId,
        updates
      );
    });
  });
});

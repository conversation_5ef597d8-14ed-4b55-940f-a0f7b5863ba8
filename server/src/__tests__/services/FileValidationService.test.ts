import { FileValidationService } from "../../services/FileValidationService";

// Mock S3 config
jest.mock("../../config/s3Config", () => ({
  getS3Config: jest.fn(() => ({
    maxFileSize: 100 * 1024 * 1024, // 100MB
    allowedFileTypes: ["image/jpeg", "image/png", "video/mp4"],
  })),
}));

describe("FileValidationService", () => {
  let fileValidationService: FileValidationService;

  beforeEach(() => {
    fileValidationService = new FileValidationService();
  });

  describe("validateFile", () => {
    it("should validate a valid image file", async () => {
      const result = await fileValidationService.validateFile(
        "test-image.jpg",
        1024 * 1024, // 1MB
        "image/jpeg"
      );

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.metadata?.fileSize).toBe(1024 * 1024);
    });

    it("should reject file with empty name", async () => {
      const result = await fileValidationService.validateFile(
        "",
        1024 * 1024,
        "image/jpeg"
      );

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(
        result.errors.some((error) => error.code === "INVALID_FILE_NAME")
      ).toBe(true);
      expect(
        result.errors.find((error) => error.code === "INVALID_FILE_NAME")
          ?.message
      ).toContain("empty");
    });

    it("should reject file with invalid characters in name", async () => {
      const result = await fileValidationService.validateFile(
        "test<>file.jpg",
        1024 * 1024,
        "image/jpeg"
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe("INVALID_FILE_NAME");
      expect(result.errors[0].message).toContain("invalid characters");
    });

    it("should reject file with reserved name", async () => {
      const result = await fileValidationService.validateFile(
        "CON.jpg",
        1024 * 1024,
        "image/jpeg"
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe("INVALID_FILE_NAME");
      expect(result.errors[0].message).toContain("reserved");
    });

    it("should reject file that is too large", async () => {
      const result = await fileValidationService.validateFile(
        "large-file.jpg",
        200 * 1024 * 1024, // 200MB (exceeds 100MB limit)
        "image/jpeg"
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe("FILE_TOO_LARGE");
      expect(result.errors[0].value).toBe(200 * 1024 * 1024);
      expect(result.errors[0].limit).toBe(100 * 1024 * 1024);
    });

    it("should reject file with zero size", async () => {
      const result = await fileValidationService.validateFile(
        "empty-file.jpg",
        0,
        "image/jpeg"
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe("FILE_TOO_LARGE");
      expect(result.errors[0].message).toContain("greater than 0");
    });

    it("should reject unsupported MIME type", async () => {
      const result = await fileValidationService.validateFile(
        "document.pdf",
        1024 * 1024,
        "application/pdf"
      );

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(
        result.errors.some((error) => error.code === "INVALID_FILE_TYPE")
      ).toBe(true);
      expect(
        result.errors.some((error) => error.value === "application/pdf")
      ).toBe(true);
    });

    it("should reject file without extension", async () => {
      const result = await fileValidationService.validateFile(
        "filename_without_extension",
        1024 * 1024,
        "image/jpeg"
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe("INVALID_FILE_TYPE");
      expect(result.errors[0].message).toContain("extension");
    });

    it("should reject unsupported file extension", async () => {
      const result = await fileValidationService.validateFile(
        "document.pdf",
        1024 * 1024,
        "image/jpeg" // MIME type is allowed but extension is not
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe("INVALID_FILE_TYPE");
      expect(result.errors[0].value).toBe(".pdf");
    });

    it("should warn about MIME type and extension mismatch", async () => {
      const result = await fileValidationService.validateFile(
        "image.jpg",
        1024 * 1024,
        "video/mp4" // Extension says image, MIME type says video
      );

      expect(result.isValid).toBe(true); // Still valid, but with warnings
      expect(result.warnings).toBeDefined();
      expect(result.warnings).toHaveLength(1);
      expect(result.warnings![0]).toContain("may not match");
    });

    it("should handle multiple validation errors", async () => {
      const result = await fileValidationService.validateFile(
        "", // Empty name
        200 * 1024 * 1024, // Too large
        "application/pdf" // Unsupported type
      );

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(1);

      const errorCodes = result.errors.map((error) => error.code);
      expect(errorCodes).toContain("INVALID_FILE_NAME");
      expect(errorCodes).toContain("FILE_TOO_LARGE");
      expect(errorCodes).toContain("INVALID_FILE_TYPE");
    });

    it("should validate video files correctly", async () => {
      const result = await fileValidationService.validateFile(
        "video.mp4",
        50 * 1024 * 1024, // 50MB
        "video/mp4"
      );

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it("should handle very long file names", async () => {
      const longFileName = "a".repeat(300) + ".jpg"; // 304 characters

      const result = await fileValidationService.validateFile(
        longFileName,
        1024 * 1024,
        "image/jpeg"
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe("INVALID_FILE_NAME");
      expect(result.errors[0].message).toContain("too long");
      expect(result.errors[0].limit).toBe(255);
    });
  });

  describe("updateConfig", () => {
    it("should update validation configuration", () => {
      const newConfig = {
        maxFileSize: 50 * 1024 * 1024, // 50MB
        allowedMimeTypes: ["image/jpeg", "image/png"],
      };

      fileValidationService.updateConfig(newConfig);
      const config = fileValidationService.getConfig();

      expect(config.maxFileSize).toBe(50 * 1024 * 1024);
      expect(config.allowedMimeTypes).toEqual(["image/jpeg", "image/png"]);
    });
  });

  describe("getConfig", () => {
    it("should return current configuration", () => {
      const config = fileValidationService.getConfig();

      expect(config).toHaveProperty("maxFileSize");
      expect(config).toHaveProperty("allowedMimeTypes");
      expect(config).toHaveProperty("allowedExtensions");
      expect(config).toHaveProperty("scanForMalware");
      expect(config).toHaveProperty("checkFileIntegrity");
    });
  });
});

import { S3Service } from "../../services/S3Service";
import { MediaRepository } from "../../repositories/MediaRepository";
import { ThumbnailService } from "../../services/ThumbnailService";
import { SignedUrlRequest, MultipartUploadRequest } from "../../types/media";

// Mock dependencies
jest.mock("../../repositories/MediaRepository");
jest.mock("../../services/ThumbnailService");
jest.mock("../../config/s3Config", () => ({
  createS3Client: jest.fn(() => ({
    send: jest.fn(),
  })),
  getS3Config: jest.fn(() => ({
    bucketName: "test-bucket",
    region: "us-east-1",
    accessKeyId: "test-key",
    secretAccessKey: "test-secret",
    signedUrlExpires: 900,
    maxFileSize: 104857600,
    allowedFileTypes: ["image/jpeg", "image/png", "video/mp4"],
  })),
  generateFileKey: jest.fn(() => "test-file-key"),
  isFileTypeAllowed: jest.fn(() => true),
  isFileSizeAllowed: jest.fn(() => true),
}));

// Mock AWS SDK
jest.mock("@aws-sdk/s3-request-presigner", () => ({
  getSignedUrl: jest.fn(() => Promise.resolve("https://test-signed-url.com")),
}));

jest.mock("@aws-sdk/client-s3", () => ({
  S3Client: jest.fn(),
  PutObjectCommand: jest.fn(),
  CreateMultipartUploadCommand: jest.fn(),
  UploadPartCommand: jest.fn(),
  CompleteMultipartUploadCommand: jest.fn(),
  AbortMultipartUploadCommand: jest.fn(),
  HeadObjectCommand: jest.fn(),
  DeleteObjectCommand: jest.fn(),
  ListObjectsV2Command: jest.fn(),
  GetObjectCommand: jest.fn(),
}));

describe("S3Service", () => {
  let s3Service: S3Service;
  let mockMediaRepository: jest.Mocked<MediaRepository>;
  let mockThumbnailService: jest.Mocked<ThumbnailService>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup mocks
    mockMediaRepository = {
      getUserStorageQuota: jest.fn(),
      calculateUserStorage: jest.fn(),
      saveMediaMetadata: jest.fn(),
      updateUserStorageQuota: jest.fn(),
      getMediaByFileKey: jest.fn(),
      deleteMedia: jest.fn(),
    } as any;

    mockThumbnailService = {
      createThumbnailTask: jest.fn(),
    } as any;

    // Mock constructor dependencies
    (MediaRepository as jest.Mock).mockImplementation(() => mockMediaRepository);
    (ThumbnailService as jest.Mock).mockImplementation(() => mockThumbnailService);

    s3Service = new S3Service();
  });

  describe("generateSignedUrl", () => {
    it("should generate signed URL for valid file", async () => {
      const request: SignedUrlRequest = {
        fileName: "test.jpg",
        fileType: "image/jpeg",
        fileSize: 1024 * 1024, // 1MB
      };

      const userId = "test-user-id";

      // Mock quota check
      mockMediaRepository.getUserStorageQuota.mockResolvedValue({
        userId,
        usedStorage: 0,
        storageLimit: 1024 * 1024 * 1024, // 1GB
        fileCountLimit: 1000,
        currentFileCount: 0,
        lastUpdated: new Date(),
      });

      mockMediaRepository.calculateUserStorage.mockResolvedValue({
        usedStorage: 0,
        fileCount: 0,
      });

      const result = await s3Service.generateSignedUrl(request, userId);

      expect(result).toHaveProperty("url");
      expect(result).toHaveProperty("fileKey");
      expect(result).toHaveProperty("method", "PUT");
      expect(result).toHaveProperty("expiresAt");
      expect(typeof result.expiresAt).toBe("number");
    });

    it("should reject file that exceeds size limit", async () => {
      const request: SignedUrlRequest = {
        fileName: "large-file.jpg",
        fileType: "image/jpeg",
        fileSize: 200 * 1024 * 1024, // 200MB (exceeds 100MB limit)
      };

      const userId = "test-user-id";

      // Mock isFileSizeAllowed to return false
      const { isFileSizeAllowed } = require("../../config/s3Config");
      isFileSizeAllowed.mockReturnValue(false);

      await expect(s3Service.generateSignedUrl(request, userId)).rejects.toThrow();
    });

    it("should reject unsupported file type", async () => {
      const request: SignedUrlRequest = {
        fileName: "document.pdf",
        fileType: "application/pdf",
        fileSize: 1024 * 1024,
      };

      const userId = "test-user-id";

      // Mock isFileTypeAllowed to return false
      const { isFileTypeAllowed } = require("../../config/s3Config");
      isFileTypeAllowed.mockReturnValue(false);

      await expect(s3Service.generateSignedUrl(request, userId)).rejects.toThrow();
    });

    it("should reject when user quota is exceeded", async () => {
      const request: SignedUrlRequest = {
        fileName: "test.jpg",
        fileType: "image/jpeg",
        fileSize: 1024 * 1024,
      };

      const userId = "test-user-id";

      // Mock quota exceeded
      mockMediaRepository.getUserStorageQuota.mockResolvedValue({
        userId,
        usedStorage: 1024 * 1024 * 1024, // 1GB used
        storageLimit: 1024 * 1024 * 1024, // 1GB limit
        fileCountLimit: 1000,
        currentFileCount: 500,
        lastUpdated: new Date(),
      });

      mockMediaRepository.calculateUserStorage.mockResolvedValue({
        usedStorage: 1024 * 1024 * 1024, // 1GB used
        fileCount: 500,
      });

      await expect(s3Service.generateSignedUrl(request, userId)).rejects.toThrow("Storage quota exceeded");
    });
  });

  describe("initiateMultipartUpload", () => {
    it("should initiate multipart upload for large file", async () => {
      const request: MultipartUploadRequest = {
        fileName: "large-video.mp4",
        fileType: "video/mp4",
        fileSize: 200 * 1024 * 1024, // 200MB
        partSize: 10 * 1024 * 1024, // 10MB parts
      };

      const userId = "test-user-id";

      // Mock quota check
      mockMediaRepository.getUserStorageQuota.mockResolvedValue({
        userId,
        usedStorage: 0,
        storageLimit: 1024 * 1024 * 1024,
        fileCountLimit: 1000,
        currentFileCount: 0,
        lastUpdated: new Date(),
      });

      mockMediaRepository.calculateUserStorage.mockResolvedValue({
        usedStorage: 0,
        fileCount: 0,
      });

      // Mock S3 response
      const mockS3Client = require("../../config/s3Config").createS3Client();
      mockS3Client.send.mockResolvedValue({
        UploadId: "test-upload-id",
      });

      const result = await s3Service.initiateMultipartUpload(request, userId);

      expect(result).toHaveProperty("uploadId", "test-upload-id");
      expect(result).toHaveProperty("fileKey");
      expect(result).toHaveProperty("partSize", 10 * 1024 * 1024);
      expect(result).toHaveProperty("totalParts", 20); // 200MB / 10MB = 20 parts
    });
  });

  describe("handleUploadComplete", () => {
    it("should save media metadata and trigger thumbnail generation", async () => {
      const request = {
        fileKey: "test-file-key",
        fileName: "test-image.jpg",
        fileType: "image/jpeg",
        fileSize: 1024 * 1024,
        metadata: {
          width: 1920,
          height: 1080,
        },
      };

      const userId = "test-user-id";

      // Mock S3 head object (file exists)
      const mockS3Client = require("../../config/s3Config").createS3Client();
      mockS3Client.send.mockResolvedValue({});

      // Mock media repository
      const mockMediaMetadata = {
        id: "test-media-id",
        userId,
        fileName: request.fileName,
        fileKey: request.fileKey,
        fileType: request.fileType,
        fileSize: request.fileSize,
        uploadDate: new Date(),
        url: `https://test-bucket.s3.us-east-1.amazonaws.com/${request.fileKey}`,
        width: 1920,
        height: 1080,
        status: "ready" as const,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockMediaRepository.saveMediaMetadata.mockResolvedValue(mockMediaMetadata);
      mockMediaRepository.calculateUserStorage.mockResolvedValue({
        usedStorage: 1024 * 1024,
        fileCount: 1,
      });
      mockMediaRepository.updateUserStorageQuota.mockResolvedValue({
        userId,
        usedStorage: 1024 * 1024,
        storageLimit: 1024 * 1024 * 1024,
        fileCountLimit: 1000,
        currentFileCount: 1,
        lastUpdated: new Date(),
      });

      // Mock thumbnail service
      mockThumbnailService.createThumbnailTask.mockResolvedValue("thumbnail-task-id");

      const result = await s3Service.handleUploadComplete(request, userId);

      expect(result).toEqual(mockMediaMetadata);
      expect(mockMediaRepository.saveMediaMetadata).toHaveBeenCalledWith({
        userId,
        fileName: request.fileName,
        fileKey: request.fileKey,
        fileType: request.fileType,
        fileSize: request.fileSize,
        uploadDate: expect.any(Date),
        url: expect.stringContaining(request.fileKey),
        width: 1920,
        height: 1080,
        status: "ready",
      });
      expect(mockThumbnailService.createThumbnailTask).toHaveBeenCalledWith(
        "test-media-id",
        request.fileKey,
        request.fileType,
        ["small", "medium", "large"]
      );
    });

    it("should throw error if file not found in S3", async () => {
      const request = {
        fileKey: "non-existent-file-key",
        fileName: "test.jpg",
        fileType: "image/jpeg",
        fileSize: 1024,
      };

      const userId = "test-user-id";

      // Mock S3 head object error (file not found)
      const mockS3Client = require("../../config/s3Config").createS3Client();
      mockS3Client.send.mockRejectedValue(new Error("NoSuchKey"));

      await expect(s3Service.handleUploadComplete(request, userId)).rejects.toThrow("File not found in S3");
    });
  });

  describe("deleteFile", () => {
    it("should delete file from S3 and update metadata", async () => {
      const fileKey = "test-file-key";
      const userId = "test-user-id";

      // Mock media metadata
      const mockMedia = {
        id: "test-media-id",
        userId,
        fileKey,
        fileName: "test.jpg",
        fileType: "image/jpeg",
        fileSize: 1024 * 1024,
        uploadDate: new Date(),
        url: `https://test-bucket.s3.us-east-1.amazonaws.com/${fileKey}`,
        status: "ready" as const,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockMediaRepository.getMediaByFileKey.mockResolvedValue(mockMedia);
      mockMediaRepository.deleteMedia.mockResolvedValue(true);
      mockMediaRepository.calculateUserStorage.mockResolvedValue({
        usedStorage: 0,
        fileCount: 0,
      });
      mockMediaRepository.updateUserStorageQuota.mockResolvedValue({
        userId,
        usedStorage: 0,
        storageLimit: 1024 * 1024 * 1024,
        fileCountLimit: 1000,
        currentFileCount: 0,
        lastUpdated: new Date(),
      });

      // Mock S3 delete
      const mockS3Client = require("../../config/s3Config").createS3Client();
      mockS3Client.send.mockResolvedValue({});

      const result = await s3Service.deleteFile(fileKey, userId);

      expect(result).toBe(true);
      expect(mockMediaRepository.deleteMedia).toHaveBeenCalledWith("test-media-id");
      expect(mockMediaRepository.updateUserStorageQuota).toHaveBeenCalled();
    });

    it("should return false if deletion fails", async () => {
      const fileKey = "test-file-key";
      const userId = "test-user-id";

      // Mock S3 delete error
      const mockS3Client = require("../../config/s3Config").createS3Client();
      mockS3Client.send.mockRejectedValue(new Error("Access denied"));

      const result = await s3Service.deleteFile(fileKey, userId);

      expect(result).toBe(false);
    });
  });
});

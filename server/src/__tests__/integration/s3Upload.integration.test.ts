import request from "supertest";
import express from "express";
import s3Routes from "../../routes/s3Routes";

// Mock all external dependencies
jest.mock("../../config/s3Config");
jest.mock("../../services/S3Service");
jest.mock("../../repositories/MediaRepository");
jest.mock("../../services/ThumbnailService");
jest.mock("../../services/FileValidationService");
jest.mock("../../services/QuotaManagementService");

describe("S3 Upload Integration Tests", () => {
  let app: express.Application;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    app.use("/api/s3", s3Routes);
  });

  describe("POST /api/s3/signed-url", () => {
    it("should generate signed URL for authenticated user with valid file", async () => {
      const response = await request(app)
        .post("/api/s3/signed-url")
        .set("x-user-id", "test-user")
        .set("x-user-type", "free")
        .send({
          fileName: "test-image.jpg",
          fileType: "image/jpeg",
          fileSize: 1024 * 1024, // 1MB
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("url");
      expect(response.body).toHaveProperty("fileKey");
      expect(response.body).toHaveProperty("method");
      expect(response.body).toHaveProperty("expiresAt");
    });

    it("should reject request without authentication", async () => {
      const response = await request(app)
        .post("/api/s3/signed-url")
        .send({
          fileName: "test-image.jpg",
          fileType: "image/jpeg",
          fileSize: 1024 * 1024,
        });

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty("error", "Authentication required");
    });

    it("should reject request with missing required fields", async () => {
      const response = await request(app)
        .post("/api/s3/signed-url")
        .set("x-user-id", "test-user")
        .send({
          fileName: "test-image.jpg",
          // Missing fileType and fileSize
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty("error");
      expect(response.body.error).toContain("Missing required fields");
    });

    it("should apply rate limiting", async () => {
      // Make multiple requests quickly to trigger rate limiting
      const requests = Array(25).fill(null).map(() =>
        request(app)
          .post("/api/s3/signed-url")
          .set("x-user-id", "test-user")
          .set("x-user-type", "free")
          .send({
            fileName: "test-image.jpg",
            fileType: "image/jpeg",
            fileSize: 1024 * 1024,
          })
      );

      const responses = await Promise.all(requests);
      
      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe("POST /api/s3/multipart/initiate", () => {
    it("should initiate multipart upload for large file", async () => {
      const response = await request(app)
        .post("/api/s3/multipart/initiate")
        .set("x-user-id", "test-user")
        .set("x-user-type", "premium")
        .send({
          fileName: "large-video.mp4",
          fileType: "video/mp4",
          fileSize: 200 * 1024 * 1024, // 200MB
          partSize: 10 * 1024 * 1024, // 10MB
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("uploadId");
      expect(response.body).toHaveProperty("fileKey");
      expect(response.body).toHaveProperty("partSize");
      expect(response.body).toHaveProperty("totalParts");
    });

    it("should apply stricter rate limiting for multipart uploads", async () => {
      // Make multiple multipart initiation requests
      const requests = Array(15).fill(null).map(() =>
        request(app)
          .post("/api/s3/multipart/initiate")
          .set("x-user-id", "test-user")
          .set("x-user-type", "free")
          .send({
            fileName: "large-video.mp4",
            fileType: "video/mp4",
            fileSize: 200 * 1024 * 1024,
          })
      );

      const responses = await Promise.all(requests);
      
      // Should hit rate limit faster than regular uploads
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe("POST /api/s3/multipart/part-url", () => {
    it("should generate part upload URL", async () => {
      const response = await request(app)
        .post("/api/s3/multipart/part-url")
        .set("x-user-id", "test-user")
        .send({
          uploadId: "test-upload-id",
          fileKey: "test-file-key",
          partNumber: 1,
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("url");
      expect(response.body).toHaveProperty("partNumber", 1);
      expect(response.body).toHaveProperty("expiresAt");
    });

    it("should reject request with missing fields", async () => {
      const response = await request(app)
        .post("/api/s3/multipart/part-url")
        .set("x-user-id", "test-user")
        .send({
          uploadId: "test-upload-id",
          // Missing fileKey and partNumber
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty("error");
    });
  });

  describe("POST /api/s3/multipart/complete", () => {
    it("should complete multipart upload", async () => {
      const response = await request(app)
        .post("/api/s3/multipart/complete")
        .set("x-user-id", "test-user")
        .send({
          uploadId: "test-upload-id",
          fileKey: "test-file-key",
          parts: [
            { partNumber: 1, etag: "etag1" },
            { partNumber: 2, etag: "etag2" },
          ],
          metadata: {
            fileName: "test-video.mp4",
            fileType: "video/mp4",
            fileSize: 200 * 1024 * 1024,
            width: 1920,
            height: 1080,
            duration: 120,
          },
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("id");
      expect(response.body).toHaveProperty("fileName", "test-video.mp4");
      expect(response.body).toHaveProperty("status", "ready");
    });
  });

  describe("POST /api/s3/multipart/abort", () => {
    it("should abort multipart upload", async () => {
      const response = await request(app)
        .post("/api/s3/multipart/abort")
        .set("x-user-id", "test-user")
        .send({
          uploadId: "test-upload-id",
          fileKey: "test-file-key",
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("success", true);
    });
  });

  describe("POST /api/s3/upload-complete", () => {
    it("should handle upload completion notification", async () => {
      const response = await request(app)
        .post("/api/s3/upload-complete")
        .set("x-user-id", "test-user")
        .send({
          fileKey: "test-file-key",
          fileName: "test-image.jpg",
          fileType: "image/jpeg",
          fileSize: 1024 * 1024,
          metadata: {
            width: 1920,
            height: 1080,
          },
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("id");
      expect(response.body).toHaveProperty("fileName", "test-image.jpg");
    });
  });

  describe("GET /api/s3/media", () => {
    it("should list user media with pagination", async () => {
      const response = await request(app)
        .get("/api/s3/media")
        .set("x-user-id", "test-user")
        .query({
          page: 1,
          limit: 10,
          fileType: "image",
          sortBy: "uploadDate",
          sortOrder: "desc",
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("items");
      expect(response.body).toHaveProperty("total");
      expect(response.body).toHaveProperty("page", 1);
      expect(response.body).toHaveProperty("limit", 10);
      expect(response.body).toHaveProperty("totalPages");
      expect(response.body).toHaveProperty("hasNext");
      expect(response.body).toHaveProperty("hasPrev");
    });

    it("should support search functionality", async () => {
      const response = await request(app)
        .get("/api/s3/media")
        .set("x-user-id", "test-user")
        .query({
          search: "vacation",
          fileType: "image",
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("items");
    });
  });

  describe("GET /api/s3/media/:id", () => {
    it("should get media details for owned media", async () => {
      const response = await request(app)
        .get("/api/s3/media/test-media-id")
        .set("x-user-id", "test-user");

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("id", "test-media-id");
      expect(response.body).toHaveProperty("fileName");
      expect(response.body).toHaveProperty("fileType");
      expect(response.body).toHaveProperty("fileSize");
      expect(response.body).toHaveProperty("url");
    });

    it("should return 404 for non-existent media", async () => {
      const response = await request(app)
        .get("/api/s3/media/non-existent-id")
        .set("x-user-id", "test-user");

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty("error", "Media not found");
    });
  });

  describe("DELETE /api/s3/media/:id", () => {
    it("should delete owned media", async () => {
      const response = await request(app)
        .delete("/api/s3/media/test-media-id")
        .set("x-user-id", "test-user")
        .set("x-user-type", "free");

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("success", true);
    });

    it("should return 403 for media owned by another user", async () => {
      const response = await request(app)
        .delete("/api/s3/media/other-user-media-id")
        .set("x-user-id", "test-user");

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty("error", "Access denied");
    });
  });

  describe("GET /api/s3/quota", () => {
    it("should return user quota information", async () => {
      const response = await request(app)
        .get("/api/s3/quota")
        .set("x-user-id", "test-user")
        .set("x-user-type", "premium");

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("userId", "test-user");
      expect(response.body).toHaveProperty("usedStorage");
      expect(response.body).toHaveProperty("storageLimit");
      expect(response.body).toHaveProperty("fileCountLimit");
      expect(response.body).toHaveProperty("currentFileCount");
      expect(response.body).toHaveProperty("availableStorage");
      expect(response.body).toHaveProperty("availableFileCount");
    });
  });

  describe("Error handling", () => {
    it("should handle internal server errors gracefully", async () => {
      // Mock a service to throw an error
      const { S3Service } = require("../../services/S3Service");
      S3Service.prototype.generateSignedUrl = jest.fn().mockRejectedValue(new Error("Internal error"));

      const response = await request(app)
        .post("/api/s3/signed-url")
        .set("x-user-id", "test-user")
        .send({
          fileName: "test.jpg",
          fileType: "image/jpeg",
          fileSize: 1024,
        });

      expect(response.status).toBe(500);
      expect(response.body).toHaveProperty("error");
      expect(response.body).toHaveProperty("details");
    });

    it("should validate request content type", async () => {
      const response = await request(app)
        .post("/api/s3/signed-url")
        .set("x-user-id", "test-user")
        .set("Content-Type", "text/plain")
        .send("invalid json");

      expect(response.status).toBe(400);
    });
  });
});

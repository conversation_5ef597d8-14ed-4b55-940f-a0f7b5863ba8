// Jest setup file for S3 media upload tests

// Mock environment variables
process.env.NODE_ENV = "test";
process.env.S3_BUCKET_NAME = "test-bucket";
process.env.S3_REGION = "us-east-1";
process.env.S3_ACCESS_KEY_ID = "test-access-key";
process.env.S3_SECRET_ACCESS_KEY = "test-secret-key";
process.env.S3_SIGNED_URL_EXPIRES = "900";
process.env.S3_MAX_FILE_SIZE = "104857600"; // 100MB
process.env.S3_ALLOWED_FILE_TYPES =
  "image/jpeg,image/png,image/gif,video/mp4,audio/mp3";

// Mock AWS SDK globally
jest.mock("@aws-sdk/client-s3", () => ({
  S3Client: jest.fn().mockImplementation(() => ({
    send: jest.fn(),
  })),
  PutObjectCommand: jest.fn(),
  GetObjectCommand: jest.fn(),
  DeleteObjectCommand: jest.fn(),
  ListObjectsV2Command: jest.fn(),
  CreateMultipartUploadCommand: jest.fn(),
  UploadPartCommand: jest.fn(),
  CompleteMultipartUploadCommand: jest.fn(),
  AbortMultipartUploadCommand: jest.fn(),
  HeadObjectCommand: jest.fn(),
}));

jest.mock("@aws-sdk/s3-request-presigner", () => ({
  getSignedUrl: jest.fn().mockResolvedValue("https://test-signed-url.com"),
}));

// Mock logger to prevent console output during tests
jest.mock("../utils/logger", () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
}));

// Global test utilities
export const createMockMediaMetadata = (overrides = {}) => ({
  id: "test-media-id",
  userId: "test-user-id",
  fileName: "test-file.jpg",
  fileKey: "uploads/user-test-user-id/images/test-file-key.jpg",
  fileType: "image/jpeg",
  fileSize: 1024 * 1024,
  uploadDate: new Date(),
  url: "https://test-bucket.s3.us-east-1.amazonaws.com/test-file-key.jpg",
  status: "ready" as const,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

export const createMockUserQuota = (overrides = {}) => ({
  userId: "test-user-id",
  usedStorage: 100 * 1024 * 1024, // 100MB
  storageLimit: 1024 * 1024 * 1024, // 1GB
  fileCountLimit: 1000,
  currentFileCount: 50,
  lastUpdated: new Date(),
  ...overrides,
});

// Setup and teardown
beforeEach(() => {
  jest.clearAllMocks();
});

afterEach(() => {
  jest.restoreAllMocks();
});

// Increase timeout for integration tests
jest.setTimeout(30000);

import express, { Request, Response } from "express";
import cors from "cors";
import { ProgressTracker } from "./services/ProgressTracker";
import { VideoController } from "./controllers/VideoController";
import { TranscribeController } from "./controllers/TranscribeController";
import { Ai<PERSON>ontroller } from "./controllers/AiController";
import { JamendoController } from "./controllers/JamendoController";
import { ThumbnailService } from "./services/ThumbnailService";
import templateRoutes from "./routes/templateRoutes";
import s3Routes from "./routes/s3Routes";
import thumbnailRoutes from "./routes/thumbnailRoutes";
import {
  validateAiRequest,
  aiRateLimit,
  aiErrorHandler,
} from "./middleware/aiErrorHandler";
import { initializeTemplates } from "./scripts/initializeTemplates";
import logger from "./utils/logger";
import helmet from "helmet";
import rateLimit from "express-rate-limit";
import * as os from "os";
import { FFMPEG_CONFIG } from "./ffmpeg/config";

const app = express();
const port = process.env.PORT || 8080;

const progressTracker = new ProgressTracker();
const videoController = new VideoController(progressTracker);
const transcribeController = new TranscribeController();
const aiController = new AiController();
const jamendoController = new JamendoController();
const thumbnailService = new ThumbnailService();

// Middleware setup
app.use(express.json({ limit: "50mb" }));

// 添加Jamendo音频代理路由来解决跨域问题
// 处理预检请求（在CORS中间件之前）
app.options(
  "/api/proxy/jamendo-audio",
  jamendoController.handlePreflightRequest.bind(jamendoController)
);

// 应用CORS中间件到其他路由
app.use(cors());

app.get(
  "/api/proxy/jamendo-audio",
  asyncHandler(jamendoController.proxyAudioRequest.bind(jamendoController))
);

app.use(haltOnTimedout);

// Add basic security headers
app.use(helmet());

// Rate limiting - 一般API限制
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 2000, // 每15分钟最多200个请求
  standardHeaders: true, // 返回标准的RateLimit头信息
  legacyHeaders: false, // 禁用X-RateLimit-*头信息
  message: { error: "请求过于频繁，请稍后再试" },
});

// 进度查询API的特殊限制 - 更宽松
const progressLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 120, // 每分钟最多120个请求（相当于每0.5秒一个请求）
  standardHeaders: true,
  legacyHeaders: false,
  message: { error: "进度查询过于频繁，请降低查询频率" },
});

// 应用限制到不同的路由
app.use("/api/progress", progressLimiter); // 进度查询使用特殊限制
app.use("/api/", generalLimiter); // 其他API使用一般限制

// API endpoints with error handling
app.post(
  "/api/generateVideo",
  asyncHandler(videoController.generateVideo.bind(videoController))
);
app.get(
  "/api/progress/:taskId",
  asyncHandler(videoController.getProgress.bind(videoController))
);

// Add route for cancelling video generation
app.post(
  "/api/cancel/:taskId",
  asyncHandler(videoController.cancelVideo.bind(videoController))
);

// 添加下载视频的路由
app.get(
  "/api/download/:taskId",
  asyncHandler(videoController.downloadVideo.bind(videoController))
);

// New transcription API endpoints
app.post(
  "/api/transcribe",
  asyncHandler(
    transcribeController.generateSubtitles.bind(transcribeController)
  )
);
app.get(
  "/api/transcribe/:taskId/status",
  asyncHandler(
    transcribeController.getSubtitlesStatus.bind(transcribeController)
  )
);
app.get(
  "/api/transcribe/:taskId/download",
  asyncHandler(
    transcribeController.downloadSubtitles.bind(transcribeController)
  )
);

// Template routes
app.use("/api/templates", templateRoutes);

// S3 media upload routes
app.use("/api/s3", s3Routes);

// Thumbnail generation routes
app.use("/api/thumbnails", thumbnailRoutes);

// AI chat API endpoint with validation and rate limiting
app.post(
  "/api/ai/chat",
  aiRateLimit,
  validateAiRequest,
  asyncHandler(aiController.chat)
);

// AI specific error handling middleware
app.use("/api/ai", aiErrorHandler);

// General error handling middleware
app.use(
  (
    err: Error,
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) => {
    logger.error("Error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
);

function haltOnTimedout(
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
) {
  next();
}

// Utility function to handle async errors
function asyncHandler(fn: Function) {
  return (
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

// 设置定期清理临时文件的定时器（每小时执行一次）
const tempFileCleanupInterval = setInterval(() => {
  logger.info("执行定期临时文件清理");
  videoController.cleanupTempFiles(1); // 清理超过1小时的临时文件
}, 3600000); // 每小时执行一次

// 设置定期清理旧输出文件的定时器（每天执行一次）
const outputFileCleanupInterval = setInterval(() => {
  logger.info("执行定期输出文件清理");
  videoController.cleanupOldOutputFiles(7); // 清理超过7天的输出文件
}, 24 * 3600000); // 每天执行一次

// 设置系统资源监控定时器（每5分钟执行一次）
const resourceMonitorInterval = setInterval(() => {
  const freeMem = os.freemem();
  const totalMem = os.totalmem();
  const freeMemPercentage = (freeMem / totalMem) * 100;

  logger.info(
    `系统资源监控: 可用内存 ${(freeMem / 1024 / 1024 / 1024).toFixed(
      2
    )}GB (${freeMemPercentage.toFixed(2)}%)`
  );

  // 如果可用内存低于10%，主动清理一些资源
  if (freeMemPercentage < 10) {
    logger.warn("系统内存不足，主动清理资源");
    videoController.cleanupAllProcesses();
    // 强制执行垃圾回收（如果可用）
    if (global.gc) {
      try {
        global.gc();
        logger.info("已执行强制垃圾回收");
      } catch (error) {
        logger.error("执行垃圾回收失败:", error);
      }
    }
  }
}, 300000); // 每5分钟

// 设置缩略图任务清理定时器（每天执行一次）
const thumbnailCleanupInterval = setInterval(() => {
  logger.info("执行定期缩略图任务清理");
  thumbnailService.cleanupExpiredTasks(24); // 清理超过24小时的任务
}, 24 * 3600000); // 每天执行一次

// 启动时执行一次清理，确保重启后能清理之前可能遗留的资源
videoController.cleanupTempFiles(1);
videoController.cleanupOldOutputFiles(7);
thumbnailService.cleanupExpiredTasks(24);

// 初始化模板数据
initializeTemplates().catch((error) => {
  logger.error("模板初始化失败:", error);
});

// Graceful shutdown
const shutdown = (): void => {
  logger.info("服务正在关闭，清理资源...");

  // 清理所有定时器
  clearInterval(tempFileCleanupInterval);
  clearInterval(outputFileCleanupInterval);
  clearInterval(resourceMonitorInterval);
  clearInterval(thumbnailCleanupInterval);

  // 清理所有活动进程和队列中的任务
  videoController.cleanupAllProcesses();

  // 清理进度跟踪器
  progressTracker.cleanup();

  // 给清理过程一些时间完成
  setTimeout(() => {
    logger.info("所有资源已清理完毕，服务正常关闭");
    process.exit(0);
  }, 1000);
};

// 注册进程信号处理
["SIGINT", "SIGTERM"].forEach((signal) => {
  process.on(signal, () => {
    logger.info(`收到 ${signal} 信号，开始关闭服务...`);
    shutdown();
  });
});

// 处理未捕获的异常和拒绝的Promise
process.on("uncaughtException", (error) => {
  logger.error("未捕获的异常:", error);
  // 对于未捕获的异常，我们仍然尝试清理资源，但不立即退出
  // 这样可以让正在进行的任务有机会完成
  videoController.cleanupTempFiles(1);
});

process.on("unhandledRejection", (reason, promise) => {
  logger.error("未处理的Promise拒绝:", reason);
});

// 检测硬件加速
const detectHardwareAcceleration = () => {
  logger.info("正在检测可用的硬件加速...");

  // 检查是否启用了硬件加速
  if (!FFMPEG_CONFIG.HW_ACCELERATION.enabled) {
    logger.info("硬件加速已在配置中禁用");
    return;
  }

  // 检测可用的硬件加速器
  const accelerators = FFMPEG_CONFIG.HW_ACCELERATION.detect();
  const availableAccelerators = accelerators.filter((a) => a.available);

  if (availableAccelerators.length === 0) {
    logger.info("未检测到可用的硬件加速器，将使用软件编码");
  } else {
    logger.info(`检测到 ${availableAccelerators.length} 个可用的硬件加速器:`);
    availableAccelerators.forEach((a) => {
      logger.info(`- ${a.type}`);
    });

    // 获取最佳的硬件加速器
    const bestAccelerator = FFMPEG_CONFIG.HW_ACCELERATION.getBestAccelerator();
    if (bestAccelerator) {
      logger.info(`将使用 ${bestAccelerator} 作为首选硬件加速器`);
    }
  }
};

// Start the server
const server = app.listen(port, () => {
  logger.info(`服务已启动，运行在端口 ${port}`);
  logger.info(`系统临时目录: ${os.tmpdir()}`);
  logger.info(
    `系统总内存: ${(os.totalmem() / 1024 / 1024 / 1024).toFixed(2)}GB`
  );

  // 检测硬件加速
  detectHardwareAcceleration();
});

// Listen for server errors
server.on("error", (err: Error) => {
  logger.error("服务器遇到错误:", err);
});

import { S3Client } from "@aws-sdk/client-s3";
import logger from "../utils/logger";

/**
 * S3配置接口
 */
export interface S3Config {
  bucketName: string;
  region: string;
  accessKeyId?: string;
  secretAccessKey?: string;
  signedUrlExpires: number;
  maxFileSize: number;
  allowedFileTypes: string[];
}

/**
 * 获取S3配置
 */
export const getS3Config = (): S3Config => {
  console.log();
  const config: S3Config = {
    bucketName: process.env.S3_BUCKET_NAME || "",
    region: process.env.S3_REGION || process.env.AWS_REGION || "us-east-1",
    accessKeyId:
      process.env.S3_ACCESS_KEY_ID || process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey:
      process.env.S3_SECRET_ACCESS_KEY ||
      process.env.AWS_SECRET_ACCESS_KEY ||
      "",
    signedUrlExpires: parseInt(process.env.S3_SIGNED_URL_EXPIRES || "900"), // 15分钟
    maxFileSize: parseInt(process.env.S3_MAX_FILE_SIZE || "104857600"), // 100MB
    allowedFileTypes: (process.env.S3_ALLOWED_FILE_TYPES || "")
      .split(",")
      .filter(Boolean),
  };

  // 验证必需的配置
  if (!config.bucketName) {
    throw new Error("S3_BUCKET_NAME environment variable is required");
  }
  if (!config.accessKeyId) {
    throw new Error(
      "S3_ACCESS_KEY_ID or AWS_ACCESS_KEY_ID environment variable is required"
    );
  }
  if (!config.secretAccessKey) {
    throw new Error(
      "S3_SECRET_ACCESS_KEY or AWS_SECRET_ACCESS_KEY environment variable is required"
    );
  }

  return config;
};

/**
 * 创建S3客户端实例
 */
export const createS3Client = (): S3Client => {
  const config = getS3Config();

  const s3Client = new S3Client({
    region: config.region,
    // credentials: {
    //   accessKeyId: config.accessKeyId || "",
    //   secretAccessKey: config.secretAccessKey || "",
    // },
  });

  logger.info(
    `S3 client initialized for region: ${config.region}, bucket: ${config.bucketName}`
  );
  return s3Client;
};

/**
 * S3存储路径结构配置
 */
export const S3_PATHS = {
  UPLOADS: "uploads",
  THUMBNAILS: "thumbnails",
  TEMP: "temp",

  // 根据用户ID和文件类型生成路径
  getUserUploadPath: (userId: string, fileType: string): string => {
    const category = getFileCategory(fileType);
    return `${S3_PATHS.UPLOADS}/user-${userId}/${category}`;
  },

  // 生成缩略图路径
  getThumbnailPath: (fileKey: string, size: string = "medium"): string => {
    const fileName = fileKey.split("/").pop()?.split(".")[0] || "unknown";
    return `${S3_PATHS.THUMBNAILS}/${fileName}-${size}.jpg`;
  },

  // 生成临时文件路径
  getTempPath: (uploadId: string): string => {
    return `${S3_PATHS.TEMP}/${uploadId}`;
  },
};

/**
 * 根据MIME类型确定文件类别
 */
export const getFileCategory = (mimeType: string): string => {
  if (mimeType.startsWith("image/")) {
    return "images";
  } else if (mimeType.startsWith("video/")) {
    return "videos";
  } else if (mimeType.startsWith("audio/")) {
    return "audio";
  } else {
    return "other";
  }
};

/**
 * 验证文件类型是否被允许
 */
export const isFileTypeAllowed = (mimeType: string): boolean => {
  const config = getS3Config();
  if (config.allowedFileTypes.length === 0) {
    return true; // 如果没有配置限制，则允许所有类型
  }
  return config.allowedFileTypes.includes(mimeType);
};

/**
 * 验证文件大小是否在限制内
 */
export const isFileSizeAllowed = (fileSize: number): boolean => {
  const config = getS3Config();
  return fileSize <= config.maxFileSize;
};

/**
 * 生成唯一的文件键
 */
export const generateFileKey = (
  userId: string,
  fileName: string,
  mimeType: string
): string => {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const extension = fileName.split(".").pop() || "";
  const baseName = fileName.replace(/\.[^/.]+$/, ""); // 移除扩展名
  const sanitizedBaseName = baseName.replace(/[^a-zA-Z0-9\-_]/g, "_"); // 清理文件名

  const userPath = S3_PATHS.getUserUploadPath(userId, mimeType);
  return `${userPath}/${timestamp}-${randomString}-${sanitizedBaseName}.${extension}`;
};

/**
 * CORS配置建议
 */
export const CORS_CONFIG = {
  CORSRules: [
    {
      AllowedHeaders: ["*"],
      AllowedMethods: ["GET", "PUT", "POST", "DELETE", "HEAD"],
      AllowedOrigins: [
        "http://localhost:3000",
        "http://localhost:3001",
        "https://your-domain.com", // 替换为实际域名
      ],
      ExposeHeaders: ["ETag"],
      MaxAgeSeconds: 3000,
    },
  ],
};

/**
 * S3存储桶策略建议
 */
export const BUCKET_POLICY_TEMPLATE = {
  Version: "2012-10-17",
  Statement: [
    {
      Sid: "AllowDirectUpload",
      Effect: "Allow",
      Principal: {
        AWS: "arn:aws:iam::ACCOUNT-ID:user/USERNAME", // 替换为实际的IAM用户ARN
      },
      Action: [
        "s3:PutObject",
        "s3:PutObjectAcl",
        "s3:GetObject",
        "s3:DeleteObject",
      ],
      Resource: "arn:aws:s3:::BUCKET-NAME/*", // 替换为实际的存储桶名称
    },
    {
      Sid: "AllowListBucket",
      Effect: "Allow",
      Principal: {
        AWS: "arn:aws:iam::ACCOUNT-ID:user/USERNAME", // 替换为实际的IAM用户ARN
      },
      Action: "s3:ListBucket",
      Resource: "arn:aws:s3:::BUCKET-NAME", // 替换为实际的存储桶名称
    },
  ],
};

import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Typography,
  IconButton,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Pagination,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Tooltip,
} from "@mui/material";
import {
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  ViewModule as GridViewIcon,
  ViewList as ListViewIcon,
  CloudUpload as UploadIcon,
} from "@mui/icons-material";
import {
  mediaLibraryService,
  MediaMetadata,
} from "../../services/s3UploadService";
import { UnifiedFileUpload } from "../common/UnifiedFileUpload";

export interface MediaLibraryProps {
  onMediaSelect?: (media: MediaMetadata) => void;
  selectionMode?: boolean;
  allowedTypes?: string[];
  maxSelections?: number;
}

export const MediaLibrary: React.FC<MediaLibraryProps> = ({
  onMediaSelect,
  selectionMode = false,
  allowedTypes,
  maxSelections = 1,
}) => {
  const [mediaList, setMediaList] = useState<MediaMetadata[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedMedia, setSelectedMedia] = useState<MediaMetadata[]>([]);

  // 分页和过滤状态
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [fileTypeFilter, setFileTypeFilter] = useState("");
  const [sortBy, setSortBy] = useState("uploadDate");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // 对话框状态
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [deleteConfirmDialog, setDeleteConfirmDialog] = useState<{
    open: boolean;
    media: MediaMetadata | null;
  }>({ open: false, media: null });

  // 加载媒体列表
  const loadMediaList = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await mediaLibraryService.getMediaList({
        page,
        limit: 20,
        search: searchQuery || undefined,
        fileType: fileTypeFilter || undefined,
        sortBy: sortBy as any,
        sortOrder,
      });

      setMediaList(response.items);
      setTotalPages(response.totalPages);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load media");
    } finally {
      setLoading(false);
    }
  }, [page, searchQuery, fileTypeFilter, sortBy, sortOrder]);

  // 初始加载
  useEffect(() => {
    loadMediaList();
  }, [loadMediaList]);

  // 处理搜索
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setPage(1);
  }, []);

  // 处理过滤
  const handleFilterChange = useCallback((filter: string) => {
    setFileTypeFilter(filter);
    setPage(1);
  }, []);

  // 处理排序
  const handleSortChange = useCallback(
    (field: string, order: "asc" | "desc") => {
      setSortBy(field);
      setSortOrder(order);
      setPage(1);
    },
    []
  );

  // 处理媒体选择
  const handleMediaSelect = useCallback(
    (media: MediaMetadata) => {
      if (!selectionMode) {
        onMediaSelect?.(media);
        return;
      }

      if (
        allowedTypes &&
        !allowedTypes.some((type) => media.fileType.startsWith(type))
      ) {
        return;
      }

      setSelectedMedia((prev) => {
        const isSelected = prev.some((m) => m.id === media.id);

        if (isSelected) {
          return prev.filter((m) => m.id !== media.id);
        } else {
          if (prev.length >= maxSelections) {
            return [...prev.slice(1), media];
          }
          return [...prev, media];
        }
      });
    },
    [selectionMode, allowedTypes, maxSelections, onMediaSelect]
  );

  // 处理删除
  const handleDelete = useCallback(async (media: MediaMetadata) => {
    try {
      const success = await mediaLibraryService.deleteMedia(media.id);
      if (success) {
        setMediaList((prev) => prev.filter((m) => m.id !== media.id));
        setSelectedMedia((prev) => prev.filter((m) => m.id !== media.id));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to delete media");
    }
    setDeleteConfirmDialog({ open: false, media: null });
  }, []);

  // 处理上传完成
  const handleUploadComplete = useCallback(() => {
    loadMediaList();
    setShowUploadDialog(false);
  }, [loadMediaList]);

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // 获取文件类型图标
  const getFileTypeIcon = (fileType: string): string => {
    if (fileType.startsWith("image/")) return "🖼️";
    if (fileType.startsWith("video/")) return "🎥";
    if (fileType.startsWith("audio/")) return "🎵";
    return "📄";
  };

  // 渲染媒体卡片
  const renderMediaCard = (media: MediaMetadata) => {
    const isSelected = selectedMedia.some((m) => m.id === media.id);
    const isDisabled =
      allowedTypes &&
      !allowedTypes.some((type) => media.fileType.startsWith(type));

    return (
      <Card
        key={media.id}
        sx={{
          cursor: isDisabled ? "not-allowed" : "pointer",
          opacity: isDisabled ? 0.5 : 1,
          border: isSelected ? 2 : 0,
          borderColor: "primary.main",
          transition: "all 0.2s",
          "&:hover": {
            transform: isDisabled ? "none" : "translateY(-2px)",
            boxShadow: isDisabled ? 1 : 3,
          },
        }}
        onClick={() => !isDisabled && handleMediaSelect(media)}
      >
        {media.fileType.startsWith("image/") ? (
          <CardMedia
            component="img"
            height="140"
            image={media.thumbnailUrl || media.url}
            alt={media.fileName}
            sx={{ objectFit: "cover" }}
          />
        ) : (
          <Box
            sx={{
              height: 140,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "grey.100",
              fontSize: "3rem",
            }}
          >
            {getFileTypeIcon(media.fileType)}
          </Box>
        )}

        <CardContent sx={{ pb: 1 }}>
          <Tooltip title={media.fileName}>
            <Typography variant="subtitle2" noWrap sx={{ fontWeight: 500 }}>
              {media.fileName}
            </Typography>
          </Tooltip>

          <Typography variant="caption" color="text.secondary" display="block">
            {formatFileSize(media.fileSize)}
          </Typography>

          <Typography variant="caption" color="text.secondary" display="block">
            {new Date(media.uploadDate).toLocaleDateString()}
          </Typography>

          {media.width && media.height && (
            <Typography
              variant="caption"
              color="text.secondary"
              display="block"
            >
              {media.width} × {media.height}
            </Typography>
          )}

          {media.duration && (
            <Typography
              variant="caption"
              color="text.secondary"
              display="block"
            >
              {Math.round(media.duration)}s
            </Typography>
          )}

          <Chip
            label={media.status}
            size="small"
            color={
              media.status === "ready"
                ? "success"
                : media.status === "error"
                ? "error"
                : "default"
            }
            sx={{ mt: 1 }}
          />
        </CardContent>

        <CardActions sx={{ pt: 0, justifyContent: "space-between" }}>
          <Button
            size="small"
            startIcon={<DownloadIcon />}
            href={media.url}
            target="_blank"
            rel="noopener noreferrer"
            onClick={(e) => e.stopPropagation()}
          >
            下载
          </Button>

          <IconButton
            size="small"
            color="error"
            onClick={(e) => {
              e.stopPropagation();
              setDeleteConfirmDialog({ open: true, media });
            }}
          >
            <DeleteIcon />
          </IconButton>
        </CardActions>
      </Card>
    );
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* 工具栏 */}
      <Box
        sx={{
          mb: 3,
          display: "flex",
          gap: 2,
          alignItems: "center",
          flexWrap: "wrap",
        }}
      >
        <TextField
          size="small"
          placeholder="搜索媒体文件..."
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
          InputProps={{
            startAdornment: (
              <SearchIcon sx={{ mr: 1, color: "text.secondary" }} />
            ),
          }}
          sx={{ minWidth: 200 }}
        />

        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>文件类型</InputLabel>
          <Select
            value={fileTypeFilter}
            label="文件类型"
            onChange={(e) => handleFilterChange(e.target.value)}
          >
            <MenuItem value="">全部</MenuItem>
            <MenuItem value="image">图片</MenuItem>
            <MenuItem value="video">视频</MenuItem>
            <MenuItem value="audio">音频</MenuItem>
          </Select>
        </FormControl>

        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>排序</InputLabel>
          <Select
            value={`${sortBy}-${sortOrder}`}
            label="排序"
            onChange={(e) => {
              const [field, order] = e.target.value.split("-");
              handleSortChange(field, order as "asc" | "desc");
            }}
          >
            <MenuItem value="uploadDate-desc">最新上传</MenuItem>
            <MenuItem value="uploadDate-asc">最早上传</MenuItem>
            <MenuItem value="fileName-asc">文件名 A-Z</MenuItem>
            <MenuItem value="fileName-desc">文件名 Z-A</MenuItem>
            <MenuItem value="fileSize-desc">文件大小 大-小</MenuItem>
            <MenuItem value="fileSize-asc">文件大小 小-大</MenuItem>
          </Select>
        </FormControl>

        <Box sx={{ ml: "auto", display: "flex", gap: 1 }}>
          <IconButton
            onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
            color={viewMode === "grid" ? "primary" : "default"}
          >
            {viewMode === "grid" ? <ListViewIcon /> : <GridViewIcon />}
          </IconButton>

          <Button
            variant="contained"
            startIcon={<UploadIcon />}
            onClick={() => setShowUploadDialog(true)}
          >
            上传文件
          </Button>
        </Box>
      </Box>

      {/* 选择状态显示 */}
      {selectionMode && selectedMedia.length > 0 && (
        <Alert severity="info" sx={{ mb: 2 }}>
          已选择 {selectedMedia.length} 个文件
          {maxSelections > 1 && ` (最多 ${maxSelections} 个)`}
        </Alert>
      )}

      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* 媒体列表 */}
      {loading ? (
        <Box sx={{ display: "flex", justifyContent: "center", py: 4 }}>
          <CircularProgress />
        </Box>
      ) : mediaList.length === 0 ? (
        <Box sx={{ textAlign: "center", py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            暂无媒体文件
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            点击上传按钮开始添加文件
          </Typography>
        </Box>
      ) : (
        <>
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: {
                xs: "1fr",
                sm: "repeat(2, 1fr)",
                md: "repeat(3, 1fr)",
                lg: "repeat(4, 1fr)",
              },
              gap: 2,
            }}
          >
            {mediaList.map((media) => renderMediaCard(media))}
          </Box>

          {/* 分页 */}
          {totalPages > 1 && (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 3 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={(_, newPage) => setPage(newPage)}
                color="primary"
              />
            </Box>
          )}
        </>
      )}

      {/* 上传对话框 */}
      <Dialog
        open={showUploadDialog}
        onClose={() => setShowUploadDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>上传媒体文件</DialogTitle>
        <DialogContent>
          <UnifiedFileUpload
            mode="multiple"
            variant="dropzone"
            useS3Upload={true}
            validation={{
              maxSize: 100 * 1024 * 1024, // 100MB
              allowedTypes: [
                ".jpg",
                ".png",
                ".gif",
                ".mp4",
                ".mov",
                ".mp3",
                ".wav",
              ],
            }}
            onUploadComplete={handleUploadComplete}
            placeholder="拖拽文件到此处或点击选择文件"
            description="支持图片、视频、音频文件，单个文件最大100MB"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowUploadDialog(false)}>关闭</Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteConfirmDialog.open}
        onClose={() => setDeleteConfirmDialog({ open: false, media: null })}
      >
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除文件 "{deleteConfirmDialog.media?.fileName}"
            吗？此操作无法撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setDeleteConfirmDialog({ open: false, media: null })}
          >
            取消
          </Button>
          <Button
            color="error"
            onClick={() =>
              deleteConfirmDialog.media &&
              handleDelete(deleteConfirmDialog.media)
            }
          >
            删除
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};
